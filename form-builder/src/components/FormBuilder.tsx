import React from 'react';
import AddControl from './controls/AddControl';
import type { FormControl } from '../types/FormTypes';

interface FormBuilderProps {
  addControl: (control: FormControl) => void;
}

const FormBuilder: React.FC<FormBuilderProps> = ({ addControl }) => {
  return (
    <div className="flex-1 p-6 border rounded-lg shadow-md bg-white">
      <h2 className="text-2xl font-semibold mb-4">Form Builder</h2>
      <AddControl addControl={addControl} />
    </div>
  );
};

export default FormBuilder;