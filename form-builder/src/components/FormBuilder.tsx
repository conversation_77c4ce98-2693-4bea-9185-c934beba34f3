import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import type { PaletteItem } from '../types/FormTypes';
import { DocumentTextIcon, PencilIcon, ListBulletIcon, CheckIcon } from '@heroicons/react/24/outline';

interface FormBuilderProps {
  paletteItems: PaletteItem[];
}

const PaletteControl: React.FC<{ item: PaletteItem }> = ({ item }) => {
  const { attributes, listeners, setNodeRef } = useDraggable({
    id: item.type,
    data: { source: 'palette' },
  });

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'DocumentTextIcon':
        return <DocumentTextIcon className="w-6 h-6" />;
      case 'PencilIcon':
        return <PencilIcon className="w-6 h-6" />;
      case 'ListBulletIcon':
        return <ListBulletIcon className="w-6 h-6" />;
      case 'CheckIcon':
        return <CheckIcon className="w-6 h-6" />;
      default:
        return null;
    }
  };

  return (
    <div
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      className="flex items-center p-4 border rounded-md bg-gray-50 hover:bg-gray-100 cursor-move"
    >
      {getIcon(item.icon)}
      <div className="ml-3">
        <h3 className="text-sm font-medium">{item.type.charAt(0).toUpperCase() + item.type.slice(1)}</h3>
        <p className="text-xs text-gray-500">{item.description}</p>
      </div>
    </div>
  );
};

const FormBuilder: React.FC<FormBuilderProps> = ({ paletteItems }) => {
  return (
    <div className="w-64 p-6 border rounded-lg shadow-md bg-white">
      <h2 className="text-2xl font-semibold mb-4">Controls</h2>
      <div className="space-y-4">
        {paletteItems.map((item) => (
          <PaletteControl key={item.type} item={item} />
        ))}
      </div>
    </div>
  );
};

export default FormBuilder;