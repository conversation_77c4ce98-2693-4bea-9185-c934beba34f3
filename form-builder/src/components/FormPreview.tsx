import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import FormTitle from './controls/FormTitle';
import SingleLineText from './controls/SingleLineText';
import MultipleChoice from './controls/MultipleChoice';
import Checkbox from './controls/Checkbox';
import type { FormControl } from '../types/FormTypes';

interface FormPreviewProps {
  formData: FormControl[];
  draggedType: string | null;
}

interface SortableControlProps {
  control: FormControl;
}

const SortableControl: React.FC<SortableControlProps> = ({ control }) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: control.id,
    data: { source: 'preview' },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="mb-4 p-4 border rounded-md bg-gray-50 cursor-move"
    >
      {control.type === 'title' && <FormTitle title={control.label} />}
      {control.type === 'singleLineText' && <SingleLineText label={control.label} value={control.value} />}
      {control.type === 'multipleChoice' && <MultipleChoice label={control.label} values={control.values || []} />}
      {control.type === 'checkbox' && <Checkbox label={control.label} values={control.values || []} />}
    </div>
  );
};

const FormPreview: React.FC<FormPreviewProps> = ({ formData, draggedType }) => {
  return (
    <div
      className={`flex-1 p-6 border rounded-lg shadow-md bg-white ${
        draggedType ? 'border-blue-500 border-2' : ''
      }`}
    >
      <h2 className="text-2xl font-semibold mb-4">Form Preview</h2>
      <div className="min-h-[200px]">
        {formData.length === 0 && (
          <p className="text-gray-500 text-center">Drag controls here to build your form</p>
        )}
        {formData.map((control) => (
          <SortableControl key={control.id} control={control} />
        ))}
      </div>
    </div>
  );
};

export default FormPreview;