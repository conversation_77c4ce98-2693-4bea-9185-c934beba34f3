import React, { useState } from 'react';
import type { FormControl, FormControlInput } from '../../types/FormTypes';

interface AddControlProps {
  addControl: (control: FormControl) => void;
}

const AddControl: React.FC<AddControlProps> = ({ addControl }) => {
  const [controlType, setControlType] = useState<string>('');
  const [label, setLabel] = useState<string>('');
  const [value, setValue] = useState<string>('');
  const [values, setValues] = useState<string[]>(['']);

  const handleAddControl = () => {
    const control: FormControlInput = { type: controlType as FormControl['type'], label };
    if (controlType === 'title' || controlType === 'singleLineText') {
      control.value = value;
    } else if (controlType === 'multipleChoice' || controlType === 'checkbox') {
      control.values = values.filter((val) => val.trim() !== '');
    }
    addControl({ ...control, id: `${Date.now()}-${Math.random()}` });
    setLabel('');
    setValue('');
    setValues(['']);
    setControlType('');
  };

  const handleValueChange = (index: number, newValue: string) => {
    const newValues = [...values];
    newValues[index] = newValue;
    setValues(newValues);
  };

  const addValue = () => {
    setValues([...values, '']);
  };

  return (
    <div className="space-y-4">
      <select
        value={controlType}
        onChange={(e) => setControlType(e.target.value)}
        className="w-full p-2 border rounded-md"
      >
        <option value="">Select Control Type</option>
        <option value="title">Title</option>
        <option value="singleLineText">Single Line Text</option>
        <option value="multipleChoice">Multiple Choice</option>
        <option value="checkbox">Checkbox</option>
      </select>

      {controlType && (
        <input
          type="text"
          value={label}
          onChange={(e) => setLabel(e.target.value)}
          placeholder="Enter label"
          className="w-full p-2 border rounded-md"
        />
      )}

      {(controlType === 'title' || controlType === 'singleLineText') && (
        <input
          type="text"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          placeholder="Enter value"
          className="w-full p-2 border rounded-md"
        />
      )}

      {(controlType === 'multipleChoice' || controlType === 'checkbox') && (
        <div className="space-y-2">
          {values.map((val, index) => (
            <input
              key={index}
              type="text"
              value={val}
              onChange={(e) => handleValueChange(index, e.target.value)}
              placeholder={`Option ${index + 1}`}
              className="w-full p-2 border rounded-md"
            />
          ))}
          <button
            onClick={addValue}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            Add Option
          </button>
        </div>
      )}

      <button
        onClick={handleAddControl}
        disabled={!controlType || !label}
        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
      >
        Add Control
      </button>
    </div>
  );
};

export default AddControl;