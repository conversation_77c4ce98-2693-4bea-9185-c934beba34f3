import React from 'react';

interface SingleLineTextProps {
  label: string;
  value?: string;
}

const SingleLineText: React.FC<SingleLineTextProps> = ({ label, value }) => {
  return (
    <div>
      <label className="block text-lg font-medium text-gray-700 mb-1">{label}</label>
      <input
        type="text"
        value={value || ''}
        onChange={() => {}} // Placeholder for handling input
        placeholder="Enter your answer"
        className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
  );
};

export default SingleLineText;