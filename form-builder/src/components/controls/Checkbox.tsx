import React from 'react';

interface CheckboxProps {
  label: string;
  values: string[];
}

const Checkbox: React.FC<CheckboxProps> = ({ label, values }) => {
  return (
    <div>
      <label className="block text-lg font-medium text-gray-700 mb-1">{label}</label>
      {values.map((value, index) => (
        <div key={index} className="mb-2">
          <input type="checkbox" value={value} className="mr-2" />
          <span>{value}</span>
        </div>
      ))}
    </div>
  );
};

export default Checkbox;