import React from 'react';

interface MultipleChoiceProps {
  label: string;
  values: string[];
}

const MultipleChoice: React.FC<MultipleChoiceProps> = ({ label, values }) => {
  return (
    <div>
      <label className="block text-lg font-medium text-gray-700 mb-1">{label}</label>
      {values.map((value, index) => (
        <div key={index} className="mb-2">
          <input type="radio" name={`multiple-choice-${label}`} value={value} className="mr-2" />
          <span>{value}</span>
        </div>
      ))}
    </div>
  );
};

export default MultipleChoice;