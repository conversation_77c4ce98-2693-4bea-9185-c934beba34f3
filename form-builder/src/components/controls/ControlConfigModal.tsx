import React, { useState } from 'react';
import type { FormControlInput } from '../../types/FormTypes';

interface ControlConfigModalProps {
  controlType: FormControlInput['type'];
  onSubmit: (control: FormControlInput) => void;
  onCancel: () => void;
}

const ControlConfigModal: React.FC<ControlConfigModalProps> = ({ controlType, onSubmit, onCancel }) => {
  const [label, setLabel] = useState<string>('');
  const [value, setValue] = useState<string>('');
  const [values, setValues] = useState<string[]>(['']);

  const handleValueChange = (index: number, newValue: string) => {
    const newValues = [...values];
    newValues[index] = newValue;
    setValues(newValues);
  };

  const addValue = () => {
    setValues([...values, '']);
  };

  const handleSubmit = () => {
    const control: FormControlInput = { type: controlType, label };
    if (controlType === 'title' || controlType === 'singleLineText') {
      control.value = value;
    } else if (controlType === 'multipleChoice' || controlType === 'checkbox') {
      control.values = values.filter((val) => val.trim() !== '');
    }
    onSubmit(control);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="bg-white p-6 rounded-lg shadow-lg w-96">
        <h2 className="text-xl font-semibold mb-4">Configure {controlType.charAt(0).toUpperCase() + controlType.slice(1)}</h2>
        <div className="space-y-4">
          <input
            type="text"
            value={label}
            onChange={(e) => setLabel(e.target.value)}
            placeholder="Enter label"
            className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {(controlType === 'title' || controlType === 'singleLineText') && (
            <input
              type="text"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder="Enter value"
              className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          )}
          {(controlType === 'multipleChoice' || controlType === 'checkbox') && (
            <div className="space-y-2">
              {values.map((val, index) => (
                <input
                  key={index}
                  type="text"
                  value={val}
                  onChange={(e) => handleValueChange(index, e.target.value)}
                  placeholder={`Option ${index + 1}`}
                  className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ))}
              <button
                onClick={addValue}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Add Option
              </button>
            </div>
          )}
          <div className="flex justify-end gap-2">
            <button
              onClick={onCancel}
              className="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={!label}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ControlConfigModal;