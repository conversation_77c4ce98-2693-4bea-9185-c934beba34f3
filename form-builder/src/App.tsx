import React, { useState } from 'react';
import { DndContext, type DragEndEvent, type DragStartEvent } from '@dnd-kit/core';
import { SortableContext, arrayMove } from '@dnd-kit/sortable';
import FormBuilder from './components/FormBuilder';
import FormPreview from './components/FormPreview';
import ControlConfigModal from './components/controls/ControlConfigModal';
import type { FormControl, FormControlInput, PaletteItem } from './types/FormTypes';

const paletteItems: PaletteItem[] = [
  { type: 'title', icon: 'DocumentTextIcon', description: 'Add a form title' },
  { type: 'singleLineText', icon: 'PencilIcon', description: 'Add a single-line text input' },
  { type: 'multipleChoice', icon: 'ListBulletIcon', description: 'Add multiple choice options' },
  { type: 'checkbox', icon: 'CheckIcon', description: 'Add checkbox options' },
];

const App: React.FC = () => {
  const [formData, setFormData] = useState<FormControl[]>([]);
  const [configControl, setConfigControl] = useState<FormControlInput | null>(null);
  const [draggedType, setDraggedType] = useState<string | null>(null);

  const handleDragStart = (event: DragStartEvent) => {
    setDraggedType(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    // Dragging from palette to preview
    if (active.data.current?.source === 'palette' && over?.data.current?.source === 'preview') {
      setConfigControl({ type: active.id as FormControl['type'], label: '' });
      return;
    }

    // Reordering within preview
    if (active.data.current?.source === 'preview' && over?.data.current?.source === 'preview') {
      const oldIndex = formData.findIndex((item) => item.id === active.id);
      const newIndex = formData.findIndex((item) => item.id === over.id);
      if (oldIndex !== newIndex) {
        setFormData(arrayMove(formData, oldIndex, newIndex));
      }
    }

    setDraggedType(null);
  };

  const handleConfigSubmit = (control: FormControlInput) => {
    setFormData([...formData, { ...control, id: `${Date.now()}-${Math.random()}` }]);
    setConfigControl(null);
  };

  const handleConfigCancel = () => {
    setConfigControl(null);
  };

  return (
    <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
      <div className="max-w-7xl mx-auto p-6">
        <h1 className="text-3xl font-bold mb-6">Form Builder</h1>
        <div className="flex gap-6">
          <SortableContext items={paletteItems.map((item) => item.type)}>
            <FormBuilder paletteItems={paletteItems} />
          </SortableContext>
          <SortableContext items={formData.map((item) => item.id)}>
            <FormPreview formData={formData} draggedType={draggedType} />
          </SortableContext>
        </div>
      </div>
      {configControl && (
        <ControlConfigModal
          controlType={configControl.type}
          onSubmit={handleConfigSubmit}
          onCancel={handleConfigCancel}
        />
      )}
    </DndContext>
  );
};

export default App;