import React, { useState } from 'react';
import { DndContext, type DragEndEvent } from '@dnd-kit/core';
import FormBuilder from './components/FormBuilder';
import FormPreview from './components/FormPreview';
import type { FormControl } from './types/FormTypes';

const App: React.FC = () => {
  const [formData, setFormData] = useState<FormControl[]>([]);

  const addControl = (control: FormControl) => {
    setFormData([...formData, { ...control, id: `${Date.now()}-${Math.random()}` }]);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    const oldIndex = formData.findIndex((item) => item.id === active.id);
    const newIndex = formData.findIndex((item) => item.id === over.id);
    const newFormData = [...formData];
    const [moved] = newFormData.splice(oldIndex, 1);
    newFormData.splice(newIndex, 0, moved);
    setFormData(newFormData);
  };

  return (
    <DndContext onDragEnd={handleDragEnd}>
      <div className="max-w-7xl mx-auto p-6">
        <h1 className="text-3xl font-bold mb-6">Form Builder</h1>
        <div className="flex gap-6">
          <FormBuilder addControl={addControl} />
          <FormPreview formData={formData} />
        </div>
      </div>
    </DndContext>
  );
};

export default App;