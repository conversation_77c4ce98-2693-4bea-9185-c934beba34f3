// src/types/FormTypes.ts
export interface FormControl {
  id: string; // Unique ID for drag-and-drop
  type: 'title' | 'singleLineText' | 'multipleChoice' | 'checkbox';
  label: string;
  value?: string; // For title, singleLineText
  values?: string[]; // For multipleChoice, checkbox
}

export interface FormControlInput {
  type: FormControl['type'];
  label: string;
  value?: string;
  values?: string[];
}