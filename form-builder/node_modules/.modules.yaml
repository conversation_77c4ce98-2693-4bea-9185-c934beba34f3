hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.2':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.28.2':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@dnd-kit/accessibility@3.1.1(react@19.1.1)':
    '@dnd-kit/accessibility': private
  '@dnd-kit/utilities@3.2.2(react@19.1.1)':
    '@dnd-kit/utilities': private
  '@esbuild/linux-x64@0.25.8':
    '@esbuild/linux-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.33.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.1':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.2':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.5':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@rolldown/pluginutils@1.0.0-beta.30':
    '@rolldown/pluginutils': private
  '@rollup/rollup-linux-x64-gnu@4.46.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.46.2':
    '@rollup/rollup-linux-x64-musl': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/hoist-non-react-statics@3.3.7(@types/react@19.1.10)':
    '@types/hoist-non-react-statics': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/react-redux@7.1.34':
    '@types/react-redux': private
  '@typescript-eslint/eslint-plugin@8.39.1(@typescript-eslint/parser@8.39.1(eslint@9.33.0)(typescript@5.8.3))(eslint@9.33.0)(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.39.1(eslint@9.33.0)(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.39.1(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.39.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.39.1(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.39.1(eslint@9.33.0)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.39.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.39.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.39.1(eslint@9.33.0)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.39.1':
    '@typescript-eslint/visitor-keys': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  balanced-match@1.0.2:
    balanced-match: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.2:
    browserslist: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001734:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  concat-map@0.0.1:
    concat-map: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-box-model@1.2.1:
    css-box-model: private
  csstype@3.1.3:
    csstype: private
  debug@4.4.1:
    debug: private
  deep-is@0.1.4:
    deep-is: private
  electron-to-chromium@1.5.200:
    electron-to-chromium: private
  esbuild@0.25.8:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  fraction.js@4.3.7:
    fraction.js: private
  gensync@1.0.0-beta.2:
    gensync: private
  glob-parent@6.0.2:
    glob-parent: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  isexe@2.0.0:
    isexe: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@5.1.1:
    lru-cache: private
  memoize-one@5.2.1:
    memoize-one: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-releases@2.0.19:
    node-releases: private
  normalize-range@0.1.2:
    normalize-range: private
  object-assign@4.1.1:
    object-assign: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prop-types@15.8.1:
    prop-types: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  raf-schd@4.0.3:
    raf-schd: private
  react-is@17.0.2:
    react-is: private
  react-redux@7.2.9(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    react-redux: private
  react-refresh@0.17.0:
    react-refresh: private
  redux@4.2.1:
    redux: private
  resolve-from@4.0.0:
    resolve-from: private
  reusify@1.1.0:
    reusify: private
  rollup@4.46.2:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  scheduler@0.26.0:
    scheduler: private
  semver@6.3.1:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  source-map-js@1.2.1:
    source-map-js: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  update-browserslist-db@1.1.3(browserslist@4.25.2):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-memo-one@1.1.3(react@19.1.1):
    use-memo-one: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  yallist@3.1.1:
    yallist: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Tue, 12 Aug 2025 14:35:53 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@esbuild/win32-x64@0.25.8'
  - '@rollup/rollup-android-arm-eabi@4.46.2'
  - '@rollup/rollup-android-arm64@4.46.2'
  - '@rollup/rollup-darwin-arm64@4.46.2'
  - '@rollup/rollup-darwin-x64@4.46.2'
  - '@rollup/rollup-freebsd-arm64@4.46.2'
  - '@rollup/rollup-freebsd-x64@4.46.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.2'
  - '@rollup/rollup-linux-arm64-gnu@4.46.2'
  - '@rollup/rollup-linux-arm64-musl@4.46.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-musl@4.46.2'
  - '@rollup/rollup-linux-s390x-gnu@4.46.2'
  - '@rollup/rollup-win32-arm64-msvc@4.46.2'
  - '@rollup/rollup-win32-ia32-msvc@4.46.2'
  - '@rollup/rollup-win32-x64-msvc@4.46.2'
  - fsevents@2.3.3
storeDir: /home/<USER>/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
