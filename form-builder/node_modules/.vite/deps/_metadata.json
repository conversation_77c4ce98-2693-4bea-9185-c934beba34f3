{"hash": "c58b2eaf", "configHash": "9d0fd286", "lockfileHash": "3b684bed", "browserHash": "5eb4b35b", "optimized": {"react": {"src": "../../.pnpm/react@19.1.1/node_modules/react/index.js", "file": "react.js", "fileHash": "5a4c55f9", "needsInterop": true}, "react-dom": {"src": "../../.pnpm/react-dom@19.1.1_react@19.1.1/node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "3fc1fb6f", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../.pnpm/react@19.1.1/node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "0411265c", "needsInterop": true}, "react/jsx-runtime": {"src": "../../.pnpm/react@19.1.1/node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4ccbf0a9", "needsInterop": true}, "@dnd-kit/core": {"src": "../../.pnpm/@dnd-kit+core@6.3.1_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/@dnd-kit/core/dist/core.esm.js", "file": "@dnd-kit_core.js", "fileHash": "6452ed61", "needsInterop": false}, "@dnd-kit/sortable": {"src": "../../.pnpm/@dnd-kit+sortable@10.0.0_@dnd-kit+core@6.3.1_react-dom@19.1.1_react@19.1.1__react@19.1.1__react@19.1.1/node_modules/@dnd-kit/sortable/dist/sortable.esm.js", "file": "@dnd-kit_sortable.js", "fileHash": "f784627e", "needsInterop": false}, "@dnd-kit/utilities": {"src": "../../.pnpm/@dnd-kit+utilities@3.2.2_react@19.1.1/node_modules/@dnd-kit/utilities/dist/utilities.esm.js", "file": "@dnd-kit_utilities.js", "fileHash": "12902349", "needsInterop": false}, "@heroicons/react/24/outline": {"src": "../../.pnpm/@heroicons+react@2.2.0_react@19.1.1/node_modules/@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "efdb2cae", "needsInterop": false}, "react-dom/client": {"src": "../../.pnpm/react-dom@19.1.1_react@19.1.1/node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "8cd89b2c", "needsInterop": true}}, "chunks": {"chunk-F3JWRXON": {"file": "chunk-F3JWRXON.js"}, "chunk-5UL6PVN7": {"file": "chunk-5UL6PVN7.js"}, "chunk-YX6KEJVD": {"file": "chunk-YX6KEJVD.js"}, "chunk-T4PUX6SL": {"file": "chunk-T4PUX6SL.js"}}}