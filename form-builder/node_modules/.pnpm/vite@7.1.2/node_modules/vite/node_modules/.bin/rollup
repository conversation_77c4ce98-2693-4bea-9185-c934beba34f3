#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/projects/nova-form/form-builder/node_modules/.pnpm/rollup@4.46.2/node_modules/rollup/dist/bin/node_modules:/home/<USER>/projects/nova-form/form-builder/node_modules/.pnpm/rollup@4.46.2/node_modules/rollup/dist/node_modules:/home/<USER>/projects/nova-form/form-builder/node_modules/.pnpm/rollup@4.46.2/node_modules/rollup/node_modules:/home/<USER>/projects/nova-form/form-builder/node_modules/.pnpm/rollup@4.46.2/node_modules:/home/<USER>/projects/nova-form/form-builder/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/projects/nova-form/form-builder/node_modules/.pnpm/rollup@4.46.2/node_modules/rollup/dist/bin/node_modules:/home/<USER>/projects/nova-form/form-builder/node_modules/.pnpm/rollup@4.46.2/node_modules/rollup/dist/node_modules:/home/<USER>/projects/nova-form/form-builder/node_modules/.pnpm/rollup@4.46.2/node_modules/rollup/node_modules:/home/<USER>/projects/nova-form/form-builder/node_modules/.pnpm/rollup@4.46.2/node_modules:/home/<USER>/projects/nova-form/form-builder/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../rollup@4.46.2/node_modules/rollup/dist/bin/rollup" "$@"
else
  exec node  "$basedir/../../../../../rollup@4.46.2/node_modules/rollup/dist/bin/rollup" "$@"
fi
